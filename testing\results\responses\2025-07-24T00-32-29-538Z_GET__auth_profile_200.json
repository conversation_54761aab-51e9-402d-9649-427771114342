{"timestamp": "2025-07-24T00:32:29.538Z", "request": {"method": "get", "url": "/auth/profile", "headers": {"Accept": "application/json", "Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************.bZkR0vsIZ_DSymtM8kzZpdax02QuD77tIQMmXJuL5gY", "User-Agent": "ATMA-E2E-Test-Client/1.0.0", "Accept-Encoding": "gzip, compress, deflate, br"}}, "response": {"status": 200, "statusText": "OK", "headers": {"content-security-policy": "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "vary": "Origin, Accept-Encoding", "access-control-allow-credentials": "true", "ratelimit-policy": "5000;w=900", "ratelimit-limit": "5000", "ratelimit-remaining": "4964", "ratelimit-reset": "848", "cross-origin-embedder-policy": "require-corp", "x-request-id": "a890da10-ba48-473e-99ff-f7ee6f02288a", "content-type": "application/json; charset=utf-8", "content-length": "272", "etag": "W/\"110-zqOM8Ie/VcggXjrgk7OrDwk9k5s\"", "date": "Thu, 24 Jul 2025 00:32:29 GMT", "connection": "keep-alive", "keep-alive": "timeout=5", "x-gateway": "ATMA-API-Gateway", "x-gateway-version": "1.0.0"}, "data": {"success": true, "message": "Success", "data": {"user": {"id": "8e790ff0-a7ec-4457-9de4-4adf8db5a619", "username": null, "email": "<EMAIL>", "user_type": "user", "is_active": true, "token_balance": 3, "last_login": null, "created_at": "2025-07-24T00:32:29.508Z", "profile": null}}}}, "isError": false, "headerValidation": {"hasSecurityHeaders": true, "hasRateLimitHeaders": false}}