{"timestamp": "2025-07-24T00:15:48.071Z", "request": {"method": "post", "url": "/auth/register", "headers": {"Accept": "application/json", "Content-Type": "application/json", "User-Agent": "ATMA-E2E-Test-Client/1.0.0", "Content-Length": "117", "Accept-Encoding": "gzip, compress, deflate, br"}, "data": "{\"email\":\"<EMAIL>\",\"password\":\"TestPassword123!\",\"username\":\"nettie_reichel148061\"}"}, "response": {"status": 400, "statusText": "Bad Request", "headers": {"content-security-policy": "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "vary": "Origin, Accept-Encoding", "access-control-allow-credentials": "true", "ratelimit-policy": "2500;w=900", "ratelimit-limit": "2500", "ratelimit-remaining": "2493", "ratelimit-reset": "880", "cross-origin-embedder-policy": "require-corp", "x-request-id": "94795b88-c5f1-4ae1-b0c9-b05f0b8e054a", "content-type": "application/json; charset=utf-8", "content-length": "133", "etag": "W/\"85-h7d7IjazakY1XLnSbYig0uXL3xI\"", "date": "Thu, 24 Jul 2025 00:15:48 GMT", "connection": "keep-alive", "keep-alive": "timeout=5", "x-gateway": "ATMA-API-Gateway", "x-gateway-version": "1.0.0"}, "data": {"success": false, "error": {"code": "VALIDATION_ERROR", "message": "Validation failed", "details": ["Email must be a valid email address"]}}}, "isError": true, "headerValidation": {"hasSecurityHeaders": true, "hasRateLimitHeaders": false}}