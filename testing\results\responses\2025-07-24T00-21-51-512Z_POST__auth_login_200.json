{"timestamp": "2025-07-24T00:21:51.512Z", "request": {"method": "post", "url": "/auth/login", "headers": {"Accept": "application/json", "Content-Type": "application/json", "User-Agent": "ATMA-E2E-Test-Client/1.0.0", "Content-Length": "62", "Accept-Encoding": "gzip, compress, deflate, br"}, "data": "{\"email\":\"<EMAIL>\",\"password\":\"TestPassword123!\"}"}, "response": {"status": 200, "statusText": "OK", "headers": {"content-security-policy": "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "vary": "Origin, Accept-Encoding", "access-control-allow-credentials": "true", "ratelimit-policy": "2500;w=900", "ratelimit-limit": "2500", "ratelimit-remaining": "2483", "ratelimit-reset": "517", "cross-origin-embedder-policy": "require-corp", "x-request-id": "028e3ac1-1d54-4f67-bea3-72c0206cde23", "content-type": "application/json; charset=utf-8", "content-length": "672", "etag": "W/\"2a0-d3j30eQu7UaLxWresh9QF35WVLo\"", "date": "Thu, 24 Jul 2025 00:21:51 GMT", "connection": "keep-alive", "keep-alive": "timeout=5", "x-gateway": "ATMA-API-Gateway", "x-gateway-version": "1.0.0"}, "data": {"success": true, "message": "Login successful", "data": {"user": {"id": "651a2cdd-a42f-4fd5-8661-6634778598db", "username": null, "email": "<EMAIL>", "user_type": "user", "is_active": true, "token_balance": 3, "last_login": "2025-07-24T00:21:51.507Z", "created_at": "2025-07-24T00:21:51.426Z", "updated_at": "2025-07-24T00:21:51.508Z"}, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************.KfZHitfndKmY8xMKj8LEb0V8Ko8YHvljyYKMoM3Avfc"}}}, "isError": false, "headerValidation": {"hasSecurityHeaders": true, "hasRateLimitHeaders": false}}