{"timestamp": "2025-07-24T00:32:29.571Z", "request": {"method": "put", "url": "/auth/profile", "headers": {"Accept": "application/json", "Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************.bZkR0vsIZ_DSymtM8kzZpdax02QuD77tIQMmXJuL5gY", "User-Agent": "ATMA-E2E-Test-Client/1.0.0", "Content-Length": "58", "Accept-Encoding": "gzip, compress, deflate, br"}, "data": "{\"username\":\"ludwig71149419\",\"full_name\":\"<PERSON>\"}"}, "response": {"status": 200, "statusText": "OK", "headers": {"content-security-policy": "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "vary": "Origin, Accept-Encoding", "access-control-allow-credentials": "true", "ratelimit-policy": "5000;w=900", "ratelimit-limit": "5000", "ratelimit-remaining": "4963", "ratelimit-reset": "848", "cross-origin-embedder-policy": "require-corp", "x-request-id": "2df64ebc-e7f4-49d0-b779-2971927b88a2", "content-type": "application/json; charset=utf-8", "content-length": "546", "etag": "W/\"222-fa0H+jdXVekm5VuisWVVZMRO3JQ\"", "date": "Thu, 24 Jul 2025 00:32:29 GMT", "connection": "keep-alive", "keep-alive": "timeout=5", "x-gateway": "ATMA-API-Gateway", "x-gateway-version": "1.0.0"}, "data": {"success": true, "message": "Success", "data": {"user": {"id": "8e790ff0-a7ec-4457-9de4-4adf8db5a619", "username": "ludwig71149419", "email": "<EMAIL>", "user_type": "user", "is_active": true, "token_balance": 3, "last_login": null, "created_at": "2025-07-24T00:32:29.508Z", "profile": {"user_id": "8e790ff0-a7ec-4457-9de4-4adf8db5a619", "full_name": "<PERSON>", "date_of_birth": null, "gender": null, "school_id": null, "created_at": "2025-07-24T00:32:29.559Z", "updated_at": "2025-07-24T00:32:29.559Z", "school": null}}, "message": "Profile updated successfully"}}}, "isError": false, "headerValidation": {"hasSecurityHeaders": true, "hasRateLimitHeaders": false}}