{"timestamp": "2025-07-24T00:22:14.959Z", "request": {"method": "post", "url": "/auth/register", "headers": {"Accept": "application/json", "Content-Type": "application/json", "User-Agent": "ATMA-E2E-Test-Client/1.0.0", "Content-Length": "99", "Accept-Encoding": "gzip, compress, deflate, br"}, "data": "{\"email\":\"testayl<PERSON><EMAIL>\",\"password\":\"TestPassword123!\",\"username\":\"hoyt_g<PERSON><PERSON>wski15534893\"}"}, "response": {"status": 201, "statusText": "Created", "headers": {"content-security-policy": "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "vary": "Origin, Accept-Encoding", "access-control-allow-credentials": "true", "ratelimit-policy": "2500;w=900", "ratelimit-limit": "2500", "ratelimit-remaining": "2478", "ratelimit-reset": "493", "cross-origin-embedder-policy": "require-corp", "x-request-id": "f5a454b7-2e25-4ef6-a7cd-ee1337002dba", "content-type": "application/json; charset=utf-8", "content-length": "662", "etag": "W/\"296-Ur+cuW1H7671eR5iL4Vo7y9QSgY\"", "date": "Thu, 24 Jul 2025 00:22:14 GMT", "connection": "keep-alive", "keep-alive": "timeout=5", "x-gateway": "ATMA-API-Gateway", "x-gateway-version": "1.0.0"}, "data": {"success": true, "message": "User registered successfully", "data": {"user": {"created_at": "2025-07-24T00:22:14.955Z", "updated_at": "2025-07-24T00:22:14.955Z", "id": "4c3b4ecc-85c2-47ea-a251-ae744a36482f", "is_active": true, "email": "<EMAIL>", "user_type": "user", "token_balance": 3, "username": null, "last_login": null}, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************.z7iVmKXTSq2B_G4dWT_kB22aKSBwXBFhH_VqTiDeAyw"}}}, "isError": false, "headerValidation": {"hasSecurityHeaders": true, "hasRateLimitHeaders": false}}