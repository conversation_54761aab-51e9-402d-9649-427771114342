{"timestamp": "2025-07-24T00:31:37.577Z", "request": {"method": "post", "url": "/auth/logout", "headers": {"Accept": "application/json", "Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************.pHBQe8WUZZBadHEMma6u0y5rbcjtQsSPqdMlQ4-cKuk", "User-Agent": "ATMA-E2E-Test-Client/1.0.0", "Accept-Encoding": "gzip, compress, deflate, br"}}, "response": {"status": 200, "statusText": "OK", "headers": {"content-security-policy": "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "vary": "Origin, Accept-Encoding", "access-control-allow-credentials": "true", "ratelimit-policy": "5000;w=900", "ratelimit-limit": "5000", "ratelimit-remaining": "4991", "ratelimit-reset": "900", "cross-origin-embedder-policy": "require-corp", "x-request-id": "e1ac3855-df67-4d6d-a574-6f5191c002eb", "content-type": "application/json; charset=utf-8", "content-length": "46", "etag": "W/\"2e-8bQArK4vV5vz6XC3Afw/zYzWAnM\"", "date": "Thu, 24 Jul 2025 00:31:37 GMT", "connection": "keep-alive", "keep-alive": "timeout=5", "x-gateway": "ATMA-API-Gateway", "x-gateway-version": "1.0.0"}, "data": {"success": true, "message": "Logout successful"}}, "isError": false, "headerValidation": {"hasSecurityHeaders": true, "hasRateLimitHeaders": false}}