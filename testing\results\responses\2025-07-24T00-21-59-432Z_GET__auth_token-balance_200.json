{"timestamp": "2025-07-24T00:21:59.432Z", "request": {"method": "get", "url": "/auth/token-balance", "headers": {"Accept": "application/json", "Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************.6fArYXKDBXn-a9AkaAg437vPfxd0zyeYRnCPt0NKsTo", "User-Agent": "ATMA-E2E-Test-Client/1.0.0", "Accept-Encoding": "gzip, compress, deflate, br"}}, "response": {"status": 200, "statusText": "OK", "headers": {"content-security-policy": "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "vary": "Origin, Accept-Encoding", "access-control-allow-credentials": "true", "ratelimit-policy": "5000;w=900", "ratelimit-limit": "5000", "ratelimit-remaining": "4969", "ratelimit-reset": "509", "cross-origin-embedder-policy": "require-corp", "x-request-id": "c58d3942-5f1f-437e-8b3f-973b0fb7e8dd", "content-type": "application/json; charset=utf-8", "content-length": "112", "etag": "W/\"70-B9nrVMqcbJvwQ3UONnji8SQnIeM\"", "date": "Thu, 24 Jul 2025 00:21:59 GMT", "connection": "keep-alive", "keep-alive": "timeout=5", "x-gateway": "ATMA-API-Gateway", "x-gateway-version": "1.0.0"}, "data": {"success": true, "message": "Success", "data": {"user_id": "bd4ce88c-0a02-48ef-aeeb-d5b0a2c2836c", "token_balance": 3}}}, "isError": false, "headerValidation": {"hasSecurityHeaders": true, "hasRateLimitHeaders": false}}