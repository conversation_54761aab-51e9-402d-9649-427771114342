{"timestamp": "2025-07-24T00:22:09.745Z", "request": {"method": "post", "url": "/assessment/submit", "headers": {"Accept": "application/json", "Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************.o-l9X3F-vZEgkg0ihAY6_6tMOmOHdIt_iH7DND1wdOE", "User-Agent": "ATMA-E2E-Test-Client/1.0.0", "Content-Length": "631", "Accept-Encoding": "gzip, compress, deflate, br"}, "data": "{\"assessmentName\":\"E2E Test Assessment\",\"riasec\":{\"realistic\":78,\"investigative\":90,\"artistic\":64,\"social\":55,\"enterprising\":89,\"conventional\":79},\"ocean\":{\"openness\":55,\"conscientiousness\":75,\"extraversion\":61,\"agreeableness\":75,\"neuroticism\":43},\"viaIs\":{\"creativity\":89,\"curiosity\":83,\"judgment\":88,\"loveOfLearning\":78,\"perspective\":60,\"bravery\":44,\"perseverance\":69,\"honesty\":50,\"zest\":55,\"love\":62,\"kindness\":64,\"socialIntelligence\":46,\"teamwork\":41,\"fairness\":69,\"leadership\":91,\"forgiveness\":76,\"humility\":92,\"prudence\":85,\"selfRegulation\":50,\"appreciationOfBeauty\":72,\"gratitude\":52,\"hope\":51,\"humor\":79,\"spirituality\":68}}"}, "response": {"status": 400, "statusText": "Bad Request", "headers": {"content-security-policy": "default-src 'self';style-src 'self' 'unsafe-inline';script-src 'self';img-src 'self' data: https:;base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';object-src 'none';script-src-attr 'none';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "vary": "Origin, Accept-Encoding", "access-control-allow-credentials": "true", "ratelimit-policy": "1000;w=3600", "ratelimit-limit": "1000", "ratelimit-remaining": "999", "ratelimit-reset": "3600", "x-powered-by": "Express", "x-idempotency-supported": "true", "x-idempotency-ttl-hours": "24", "content-type": "application/json; charset=utf-8", "content-length": "208", "etag": "W/\"d0-Z/U28PJ/Lv9PXjmeYSP/t00DHDI\"", "date": "Thu, 24 Jul 2025 00:22:09 GMT", "connection": "keep-alive", "keep-alive": "timeout=5", "x-gateway": "ATMA-API-Gateway", "x-gateway-version": "1.0.0"}, "data": {"success": false, "error": {"code": "VALIDATION_ERROR", "message": "Validation failed", "details": {"assessmentName": "Assessment name must be one of: AI-Driven Talent Mapping, AI-Based IQ Test, Custom Assessment"}}}}, "isError": true, "headerValidation": {"hasSecurityHeaders": false, "hasRateLimitHeaders": false}}