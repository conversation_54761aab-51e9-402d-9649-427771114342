{"timestamp": "2025-07-24T00:33:07.536Z", "request": {"method": "post", "url": "/assessment/submit", "headers": {"Accept": "application/json", "Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************.LkDPw0rv0mEtdt-OJIr8yRJ2is5nZNUFBdT37LNY1KY", "User-Agent": "ATMA-E2E-Test-Client/1.0.0", "Content-Length": "631", "Accept-Encoding": "gzip, compress, deflate, br"}, "data": "{\"assessmentName\":\"E2E Test Assessment\",\"riasec\":{\"realistic\":69,\"investigative\":86,\"artistic\":44,\"social\":59,\"enterprising\":67,\"conventional\":51},\"ocean\":{\"openness\":92,\"conscientiousness\":62,\"extraversion\":55,\"agreeableness\":91,\"neuroticism\":73},\"viaIs\":{\"creativity\":70,\"curiosity\":69,\"judgment\":41,\"loveOfLearning\":51,\"perspective\":70,\"bravery\":78,\"perseverance\":57,\"honesty\":73,\"zest\":76,\"love\":93,\"kindness\":52,\"socialIntelligence\":45,\"teamwork\":93,\"fairness\":84,\"leadership\":46,\"forgiveness\":80,\"humility\":81,\"prudence\":46,\"selfRegulation\":70,\"appreciationOfBeauty\":81,\"gratitude\":76,\"hope\":40,\"humor\":43,\"spirituality\":48}}"}, "response": {"status": 400, "statusText": "Bad Request", "headers": {"content-security-policy": "default-src 'self';style-src 'self' 'unsafe-inline';script-src 'self';img-src 'self' data: https:;base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';object-src 'none';script-src-attr 'none';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "vary": "Origin, Accept-Encoding", "access-control-allow-credentials": "true", "ratelimit-policy": "1000;w=3600", "ratelimit-limit": "1000", "ratelimit-remaining": "999", "ratelimit-reset": "3600", "x-powered-by": "Express", "x-idempotency-supported": "true", "x-idempotency-ttl-hours": "24", "content-type": "application/json; charset=utf-8", "content-length": "208", "etag": "W/\"d0-Z/U28PJ/Lv9PXjmeYSP/t00DHDI\"", "date": "Thu, 24 Jul 2025 00:33:07 GMT", "connection": "keep-alive", "keep-alive": "timeout=5", "x-gateway": "ATMA-API-Gateway", "x-gateway-version": "1.0.0"}, "data": {"success": false, "error": {"code": "VALIDATION_ERROR", "message": "Validation failed", "details": {"assessmentName": "Assessment name must be one of: AI-Driven Talent Mapping, AI-Based IQ Test, Custom Assessment"}}}}, "isError": true, "headerValidation": {"hasSecurityHeaders": false, "hasRateLimitHeaders": false}}