class ResponseValidator {
  constructor(logger) {
    this.logger = logger;
  }

  // Generic response structure validation
  validateResponseStructure(response, expectedFields = []) {
    const errors = [];

    // Check if response exists
    if (!response) {
      errors.push('Response is null or undefined');
      return { isValid: false, errors };
    }

    // Check success field
    if (typeof response.success !== 'boolean') {
      errors.push('Response missing or invalid "success" field');
    }

    // For successful responses, check data/message
    if (response.success) {
      // Timestamp is not required for successful responses in current API
    } else {
      // For error responses, validate error structure according to API Gateway spec
      if (!response.error) {
        errors.push('Error response missing "error" object');
      } else {
        if (!response.error.code) {
          errors.push('Error response missing "error.code" field');
        }
        if (!response.error.message) {
          errors.push('Error response missing "error.message" field');
        }
        // Timestamp is not required for error responses in current API
      }
    }

    // Check expected fields
    expectedFields.forEach(field => {
      if (!response.hasOwnProperty(field)) {
        errors.push(`Response missing required field: ${field}`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Validate API Gateway security headers
  validateSecurityHeaders(headers) {
    const errors = [];

    // Check for API Gateway headers
    if (!headers['x-gateway']) {
      errors.push('Missing X-Gateway header');
    } else if (headers['x-gateway'] !== 'ATMA-API-Gateway') {
      errors.push('Invalid X-Gateway header value');
    }

    if (!headers['x-gateway-version']) {
      errors.push('Missing X-Gateway-Version header');
    } else if (headers['x-gateway-version'] !== '1.0.0') {
      errors.push('Invalid X-Gateway-Version header value');
    }

    if (!headers['x-request-id']) {
      errors.push('Missing X-Request-ID header');
    } else if (!this.isValidUUID(headers['x-request-id'])) {
      errors.push('Invalid X-Request-ID header format (should be UUID)');
    }

    return errors;
  }

  // Validate rate limiting headers
  validateRateLimitHeaders(headers) {
    const errors = [];

    if (headers['x-ratelimit-limit'] && isNaN(parseInt(headers['x-ratelimit-limit']))) {
      errors.push('Invalid X-RateLimit-Limit header');
    }

    if (headers['x-ratelimit-remaining'] && isNaN(parseInt(headers['x-ratelimit-remaining']))) {
      errors.push('Invalid X-RateLimit-Remaining header');
    }

    if (headers['x-ratelimit-reset'] && isNaN(parseInt(headers['x-ratelimit-reset']))) {
      errors.push('Invalid X-RateLimit-Reset header');
    }

    return errors;
  }

  // Authentication response validation
  validateAuthResponse(response) {
    const validation = this.validateResponseStructure(response, ['data', 'message']);
    if (!validation.isValid) return validation;

    const { data } = response;
    const errors = [];

    // Validate user object
    if (!data.user) {
      errors.push('Response missing user object');
    } else {
      const userErrors = this.validateUserObject(data.user);
      errors.push(...userErrors);

      // Additional validation for auth response user object
      if (data.user.token_balance !== undefined && (!Number.isInteger(data.user.token_balance) || data.user.token_balance < 0)) {
        errors.push('User token_balance must be a non-negative integer');
      }
    }

    // Validate token for login/register responses
    if (data.token && !this.isValidJWT(data.token)) {
      errors.push('Response contains invalid JWT token');
    }

    // Validate message field (at response level, not data level)
    if (!response.message || typeof response.message !== 'string') {
      errors.push('Response missing or invalid message field');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // User object validation
  validateUserObject(user) {
    const errors = [];
    const requiredFields = ['id', 'email', 'username', 'user_type', 'is_active'];

    requiredFields.forEach(field => {
      if (!user.hasOwnProperty(field)) {
        errors.push(`User object missing field: ${field}`);
      }
    });

    // Validate specific fields
    if (user.id && !this.isValidUUID(user.id)) {
      errors.push('User ID is not a valid UUID');
    }

    if (user.email && !this.isValidEmail(user.email)) {
      errors.push('User email is not valid');
    }

    if (user.user_type && !['user', 'admin'].includes(user.user_type)) {
      errors.push('User type is not valid');
    }

    if (typeof user.is_active !== 'boolean') {
      errors.push('User is_active field is not boolean');
    }

    return errors;
  }

  // Assessment submission response validation
  validateAssessmentSubmissionResponse(response) {
    const validation = this.validateResponseStructure(response, ['data', 'message']);
    if (!validation.isValid) return validation;

    const { data } = response;
    const errors = [];

    // Required fields for assessment submission
    const requiredFields = ['jobId', 'status', 'estimatedProcessingTime'];
    requiredFields.forEach(field => {
      if (!data.hasOwnProperty(field)) {
        errors.push(`Assessment response missing field: ${field}`);
      }
    });

    // Validate jobId format
    if (data.jobId && !data.jobId.startsWith('job_')) {
      errors.push('JobId does not have expected format (job_*)');
    }

    // Validate status
    if (data.status && !['queued', 'processing'].includes(data.status)) {
      errors.push('Assessment status is not valid');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Assessment result validation
  validateAssessmentResult(response) {
    const validation = this.validateResponseStructure(response, ['data']);
    if (!validation.isValid) return validation;

    const { data } = response;
    const errors = [];

    // Required fields for assessment result
    const requiredFields = ['id', 'user_id', 'assessment_name', 'status', 'persona_profile'];
    requiredFields.forEach(field => {
      if (!data.hasOwnProperty(field)) {
        errors.push(`Assessment result missing field: ${field}`);
      }
    });

    // Validate persona profile
    if (data.persona_profile) {
      const personaErrors = this.validatePersonaProfile(data.persona_profile);
      errors.push(...personaErrors);
    }

    // Validate assessment data if present
    if (data.assessment_data) {
      const assessmentErrors = this.validateAssessmentData(data.assessment_data);
      errors.push(...assessmentErrors);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Persona profile validation
  validatePersonaProfile(persona) {
    const errors = [];
    const requiredFields = ['archetype', 'shortSummary', 'strengths', 'weaknesses', 'careerRecommendation'];

    requiredFields.forEach(field => {
      if (!persona.hasOwnProperty(field)) {
        errors.push(`Persona profile missing field: ${field}`);
      }
    });

    // Validate arrays
    if (persona.strengths && !Array.isArray(persona.strengths)) {
      errors.push('Persona strengths is not an array');
    }

    if (persona.weaknesses && !Array.isArray(persona.weaknesses)) {
      errors.push('Persona weaknesses is not an array');
    }

    if (persona.careerRecommendation && !Array.isArray(persona.careerRecommendation)) {
      errors.push('Persona careerRecommendation is not an array');
    }

    // Validate career recommendations
    if (persona.careerRecommendation && Array.isArray(persona.careerRecommendation)) {
      persona.careerRecommendation.forEach((career, index) => {
        if (!career.careerName) {
          errors.push(`Career recommendation ${index} missing careerName`);
        }
        if (!career.careerProspect) {
          errors.push(`Career recommendation ${index} missing careerProspect`);
        }
      });
    }

    return errors;
  }

  // Assessment data validation
  validateAssessmentData(assessmentData) {
    const errors = [];

    // Validate RIASEC scores
    if (assessmentData.riasec) {
      const riasecFields = ['realistic', 'investigative', 'artistic', 'social', 'enterprising', 'conventional'];
      riasecFields.forEach(field => {
        if (!this.isValidScore(assessmentData.riasec[field])) {
          errors.push(`RIASEC ${field} score is invalid`);
        }
      });
    }

    // Validate OCEAN scores
    if (assessmentData.ocean) {
      const oceanFields = ['openness', 'conscientiousness', 'extraversion', 'agreeableness', 'neuroticism'];
      oceanFields.forEach(field => {
        if (!this.isValidScore(assessmentData.ocean[field])) {
          errors.push(`OCEAN ${field} score is invalid`);
        }
      });
    }

    // Validate VIA-IS scores (complete list according to API spec)
    if (assessmentData.viaIs) {
      const viaIsFields = [
        'creativity', 'curiosity', 'judgment', 'loveOfLearning', 'perspective',
        'bravery', 'perseverance', 'honesty', 'zest', 'love', 'kindness',
        'socialIntelligence', 'teamwork', 'fairness', 'leadership', 'forgiveness',
        'humility', 'prudence', 'selfRegulation', 'appreciationOfBeauty',
        'gratitude', 'hope', 'humor', 'spirituality'
      ];
      viaIsFields.forEach(field => {
        if (assessmentData.viaIs[field] !== undefined && !this.isValidScore(assessmentData.viaIs[field])) {
          errors.push(`VIA-IS ${field} score is invalid`);
        }
      });
    }

    return errors;
  }

  // Chatbot response validation
  validateChatbotResponse(response) {
    const validation = this.validateResponseStructure(response, ['data']);
    if (!validation.isValid) return validation;

    const { data } = response;
    const errors = [];

    // Check for conversation or message data
    if (data.conversation) {
      const conversationErrors = this.validateConversationObject(data.conversation);
      errors.push(...conversationErrors);
    }

    if (data.user_message) {
      const messageErrors = this.validateMessageObject(data.user_message);
      errors.push(...messageErrors);
    }

    if (data.assistant_message) {
      const messageErrors = this.validateMessageObject(data.assistant_message);
      errors.push(...messageErrors);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Conversation object validation
  validateConversationObject(conversation) {
    const errors = [];
    const requiredFields = ['id', 'title', 'context_type', 'status'];

    requiredFields.forEach(field => {
      if (!conversation.hasOwnProperty(field)) {
        errors.push(`Conversation missing field: ${field}`);
      }
    });

    // Validate conversation ID format
    if (conversation.id && !conversation.id.startsWith('conv_')) {
      errors.push('Conversation ID does not have expected format (conv_*)');
    }

    return errors;
  }

  // Message object validation
  validateMessageObject(message) {
    const errors = [];
    const requiredFields = ['id', 'content', 'sender_type', 'created_at'];

    requiredFields.forEach(field => {
      if (!message.hasOwnProperty(field)) {
        errors.push(`Message missing field: ${field}`);
      }
    });

    // Validate sender type
    if (message.sender_type && !['user', 'assistant'].includes(message.sender_type)) {
      errors.push('Message sender_type is not valid');
    }

    return errors;
  }

  // WebSocket notification validation
  validateWebSocketNotification(notification, type) {
    const errors = [];

    const requiredFields = {
      'analysis-started': ['jobId', 'status', 'message', 'metadata', 'timestamp'],
      'analysis-complete': ['jobId', 'resultId', 'status', 'message', 'metadata', 'timestamp'],
      'analysis-failed': ['jobId', 'error', 'message', 'metadata', 'timestamp']
    };

    const required = requiredFields[type];
    if (!required) {
      errors.push(`Unknown notification type: ${type}`);
      return { isValid: false, errors };
    }

    required.forEach(field => {
      if (!notification.hasOwnProperty(field)) {
        errors.push(`Notification missing field: ${field}`);
      }
    });

    // Validate jobId format
    if (notification.jobId && !notification.jobId.startsWith('job_')) {
      errors.push('Notification jobId does not have expected format (job_*)');
    }

    // Validate resultId format for analysis-complete
    if (type === 'analysis-complete' && notification.resultId && !notification.resultId.startsWith('result_')) {
      errors.push('Notification resultId does not have expected format (result_*)');
    }

    // Validate status values
    const validStatuses = {
      'analysis-started': ['started'],
      'analysis-complete': ['completed'],
      'analysis-failed': ['failed']
    };
    if (notification.status && !validStatuses[type].includes(notification.status)) {
      errors.push(`Invalid status for ${type}: expected one of [${validStatuses[type].join(', ')}], got "${notification.status}"`);
    }

    // Validate timestamp
    if (notification.timestamp && !this.isValidTimestamp(notification.timestamp)) {
      errors.push('Notification timestamp is invalid');
    }

    // Validate message is non-empty string
    if (notification.message && (typeof notification.message !== 'string' || notification.message.trim() === '')) {
      errors.push('Notification message must be a non-empty string');
    }

    // Validate metadata structure according to WebSocket manual
    if (notification.metadata) {
      if (type === 'analysis-started' || type === 'analysis-complete') {
        if (!notification.metadata.assessmentName || typeof notification.metadata.assessmentName !== 'string') {
          errors.push('Notification metadata missing or invalid assessmentName');
        }
      }
      if (type === 'analysis-started') {
        if (!notification.metadata.estimatedProcessingTime || typeof notification.metadata.estimatedProcessingTime !== 'string') {
          errors.push('Analysis-started notification metadata missing or invalid estimatedProcessingTime');
        }
      }
      if (type === 'analysis-complete') {
        if (!notification.metadata.processingTime || typeof notification.metadata.processingTime !== 'string') {
          errors.push('Analysis-complete notification metadata missing or invalid processingTime');
        }
      }
      if (type === 'analysis-failed') {
        if (!notification.metadata.errorType || typeof notification.metadata.errorType !== 'string') {
          errors.push('Analysis-failed notification metadata missing or invalid errorType');
        }
        if (!notification.metadata.assessmentName || typeof notification.metadata.assessmentName !== 'string') {
          errors.push('Analysis-failed notification metadata missing or invalid assessmentName');
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Validate pagination structure according to API Gateway spec
  validatePaginationStructure(pagination) {
    const errors = [];
    const requiredFields = ['page', 'limit', 'total', 'totalPages', 'hasNext', 'hasPrev'];

    requiredFields.forEach(field => {
      if (!pagination.hasOwnProperty(field)) {
        errors.push(`Pagination missing field: ${field}`);
      }
    });

    // Validate field types
    if (pagination.page && (!Number.isInteger(pagination.page) || pagination.page < 1)) {
      errors.push('Pagination page must be a positive integer');
    }

    if (pagination.limit && (!Number.isInteger(pagination.limit) || pagination.limit < 1)) {
      errors.push('Pagination limit must be a positive integer');
    }

    if (pagination.total && (!Number.isInteger(pagination.total) || pagination.total < 0)) {
      errors.push('Pagination total must be a non-negative integer');
    }

    if (pagination.totalPages && (!Number.isInteger(pagination.totalPages) || pagination.totalPages < 0)) {
      errors.push('Pagination totalPages must be a non-negative integer');
    }

    if (pagination.hasNext !== undefined && typeof pagination.hasNext !== 'boolean') {
      errors.push('Pagination hasNext must be a boolean');
    }

    if (pagination.hasPrev !== undefined && typeof pagination.hasPrev !== 'boolean') {
      errors.push('Pagination hasPrev must be a boolean');
    }

    return errors;
  }

  // Utility validation methods
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  isValidUUID(uuid) {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }

  isValidJWT(token) {
    const jwtRegex = /^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/;
    return jwtRegex.test(token);
  }

  isValidTimestamp(timestamp) {
    const date = new Date(timestamp);
    return date instanceof Date && !isNaN(date);
  }

  isValidScore(score) {
    return typeof score === 'number' && score >= 0 && score <= 100;
  }

  // School object validation
  validateSchoolObject(school) {
    const errors = [];
    const requiredFields = ['id', 'name', 'address', 'city', 'province', 'created_at'];

    requiredFields.forEach(field => {
      if (!school.hasOwnProperty(field)) {
        errors.push(`School object missing field: ${field}`);
      }
    });

    // Validate school ID format
    if (school.id && !this.isValidUUID(school.id)) {
      errors.push('School ID is not a valid UUID');
    }

    return errors;
  }

  // Admin response validation
  validateAdminResponse(response) {
    const validation = this.validateResponseStructure(response, ['data', 'message']);
    if (!validation.isValid) return validation;

    const { data } = response;
    const errors = [];

    // Validate admin object
    if (!data.admin) {
      errors.push('Response missing admin object');
    } else {
      const adminErrors = this.validateAdminObject(data.admin);
      errors.push(...adminErrors);
    }

    // Validate token if present
    if (data.token && !this.isValidJWT(data.token)) {
      errors.push('Response contains invalid JWT token');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Admin object validation
  validateAdminObject(admin) {
    const errors = [];
    const requiredFields = ['id', 'username', 'email', 'user_type', 'is_active'];

    requiredFields.forEach(field => {
      if (!admin.hasOwnProperty(field)) {
        errors.push(`Admin object missing field: ${field}`);
      }
    });

    // Validate specific fields
    if (admin.id && !this.isValidUUID(admin.id)) {
      errors.push('Admin ID is not a valid UUID');
    }

    if (admin.email && !this.isValidEmail(admin.email)) {
      errors.push('Admin email is not valid');
    }

    if (admin.user_type && admin.user_type !== 'admin') {
      errors.push('Admin user_type must be "admin"');
    }

    if (typeof admin.is_active !== 'boolean') {
      errors.push('Admin is_active field is not boolean');
    }

    return errors;
  }

  // School response validation
  validateSchoolResponse(response) {
    const validation = this.validateResponseStructure(response, ['data']);
    if (!validation.isValid) return validation;

    const { data } = response;
    const errors = [];

    // Check for school object or schools array
    if (data.school) {
      const schoolErrors = this.validateSchoolObject(data.school);
      errors.push(...schoolErrors);
    }

    if (data.schools && Array.isArray(data.schools)) {
      data.schools.forEach((school, index) => {
        const schoolErrors = this.validateSchoolObject(school);
        schoolErrors.forEach(error => {
          errors.push(`School ${index}: ${error}`);
        });
      });
    }

    // Validate pagination if present
    if (data.pagination) {
      const paginationErrors = this.validatePaginationStructure(data.pagination);
      errors.push(...paginationErrors);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Main validation method
  validate(response, type, options = {}) {
    this.logger.debug(`Validating response type: ${type}`);

    let validation;
    switch (type) {
      case 'auth':
        validation = this.validateAuthResponse(response);
        break;
      case 'admin':
        validation = this.validateAdminResponse(response);
        break;
      case 'school':
        validation = this.validateSchoolResponse(response);
        break;
      case 'assessment-submission':
        validation = this.validateAssessmentSubmissionResponse(response);
        break;
      case 'assessment-result':
        validation = this.validateAssessmentResult(response);
        break;
      case 'chatbot':
        validation = this.validateChatbotResponse(response);
        break;
      case 'websocket-notification':
        validation = this.validateWebSocketNotification(response, options.notificationType);
        break;
      default:
        validation = this.validateResponseStructure(response, options.expectedFields || []);
    }

    if (validation.isValid) {
      this.logger.success(`Response validation passed: ${type}`);
    } else {
      this.logger.error(`Response validation failed: ${type}`, validation.errors);
    }

    return validation;
  }
}

module.exports = ResponseValidator;
