const { io } = require('socket.io-client');
const Logger = require('./lib/logger');
const ApiClient = require('./lib/api-client');
const TestDataGenerator = require('./lib/test-data');

const logger = new Logger('WebSocket Direct Test');

async function testDirectConnection() {
  logger.info('Testing direct WebSocket connection to notification service');

  // Setup test user first
  const apiClient = new ApiClient(null, logger);
  const testDataGenerator = new TestDataGenerator();
  const testUser = testDataGenerator.generateUserRegistrationData();
  
  try {
    // Register and login user
    logger.info(`Registering user: ${testUser.email}`);
    const registerResponse = await apiClient.register(testUser);
    const token = registerResponse.data.token;
    
    logger.info('Testing direct connection to notification service (port 3005)');
    
    // Test direct connection to notification service
    const directSocket = io('http://localhost:3005', {
      autoConnect: false,
      transports: ['websocket', 'polling'],
      reconnection: false,
      timeout: 10000
    });
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        logger.error('Direct connection timeout');
        directSocket.disconnect();
        reject(new Error('Connection timeout'));
      }, 15000);
      
      directSocket.on('connect', () => {
        logger.success('Direct connection to notification service successful');
        clearTimeout(timeout);
        
        // Test authentication
        directSocket.emit('authenticate', { token });
        
        directSocket.on('authenticated', (data) => {
          logger.success('Direct authentication successful', data);
          directSocket.disconnect();
          resolve(true);
        });
        
        directSocket.on('auth_error', (error) => {
          logger.error('Direct authentication failed', error);
          directSocket.disconnect();
          reject(new Error('Authentication failed'));
        });
      });
      
      directSocket.on('connect_error', (error) => {
        logger.error('Direct connection error:', error.message);
        clearTimeout(timeout);
        reject(error);
      });
      
      directSocket.connect();
    });
    
  } catch (error) {
    logger.error('Test setup failed:', error.message);
    throw error;
  }
}

async function testProxyConnection() {
  logger.info('Testing WebSocket connection through API Gateway proxy');

  // Setup test user first
  const apiClient = new ApiClient(null, logger);
  const testDataGenerator = new TestDataGenerator();
  const testUser = testDataGenerator.generateUserRegistrationData();
  
  try {
    // Register and login user
    logger.info(`Registering user: ${testUser.email}`);
    const registerResponse = await apiClient.register(testUser);
    const token = registerResponse.data.token;
    
    logger.info('Testing proxy connection through API Gateway (port 3000)');
    
    // Test proxy connection through API Gateway
    const proxySocket = io('http://localhost:3000', {
      autoConnect: false,
      transports: ['polling', 'websocket'], // Try polling first
      reconnection: false,
      timeout: 10000,
      forceNew: true
    });
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        logger.error('Proxy connection timeout');
        proxySocket.disconnect();
        reject(new Error('Connection timeout'));
      }, 15000);
      
      proxySocket.on('connect', () => {
        logger.success('Proxy connection through API Gateway successful');
        clearTimeout(timeout);
        
        // Test authentication
        proxySocket.emit('authenticate', { token });
        
        proxySocket.on('authenticated', (data) => {
          logger.success('Proxy authentication successful', data);
          proxySocket.disconnect();
          resolve(true);
        });
        
        proxySocket.on('auth_error', (error) => {
          logger.error('Proxy authentication failed', error);
          proxySocket.disconnect();
          reject(new Error('Authentication failed'));
        });
      });
      
      proxySocket.on('connect_error', (error) => {
        logger.error('Proxy connection error:', error.message);
        clearTimeout(timeout);
        reject(error);
      });
      
      proxySocket.connect();
    });
    
  } catch (error) {
    logger.error('Test setup failed:', error.message);
    throw error;
  }
}

async function main() {
  logger.info('Starting WebSocket connection diagnostics');
  
  try {
    // Test 1: Direct connection to notification service
    logger.info('='.repeat(80));
    logger.info('TEST 1: Direct connection to notification service');
    logger.info('='.repeat(80));
    
    try {
      await testDirectConnection();
      logger.success('✅ Direct connection test PASSED');
    } catch (error) {
      logger.error('❌ Direct connection test FAILED:', error.message);
    }
    
    // Test 2: Proxy connection through API Gateway
    logger.info('='.repeat(80));
    logger.info('TEST 2: Proxy connection through API Gateway');
    logger.info('='.repeat(80));
    
    try {
      await testProxyConnection();
      logger.success('✅ Proxy connection test PASSED');
    } catch (error) {
      logger.error('❌ Proxy connection test FAILED:', error.message);
    }
    
  } catch (error) {
    logger.error('Diagnostic test failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { testDirectConnection, testProxyConnection };
