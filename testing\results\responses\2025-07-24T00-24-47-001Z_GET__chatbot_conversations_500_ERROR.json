{"timestamp": "2025-07-24T00:24:47.001Z", "request": {"method": "get", "url": "/chatbot/conversations", "headers": {"Accept": "application/json", "Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************.w_HH3B1Njrv1Q1MEpM5UIw-aKVGD0wqX6Dn8_-FoAfQ", "User-Agent": "ATMA-E2E-Test-Client/1.0.0", "Accept-Encoding": "gzip, compress, deflate, br"}}, "response": {"status": 500, "statusText": "Internal Server Error", "headers": {"content-security-policy": "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "vary": "Origin, Accept-Encoding", "access-control-allow-credentials": "true", "ratelimit-policy": "1000;w=900", "ratelimit-limit": "1000", "ratelimit-remaining": "998", "ratelimit-reset": "119", "cross-origin-embedder-policy": "require-corp", "x-request-id": "f787953f-4f83-4ef8-890a-ca7557a9ab64", "content-type": "application/json; charset=utf-8", "content-length": "97", "etag": "W/\"61-bJJnY/X9XBwtUM1YQjiGWOOEb/8\"", "date": "Thu, 24 Jul 2025 00:24:46 GMT", "connection": "keep-alive", "keep-alive": "timeout=5", "x-gateway": "ATMA-API-Gateway", "x-gateway-version": "1.0.0"}, "data": {"success": false, "error": {"code": "INTERNAL_ERROR", "message": "An internal server error occurred"}}}, "isError": true, "headerValidation": {"hasSecurityHeaders": true, "hasRateLimitHeaders": false}}