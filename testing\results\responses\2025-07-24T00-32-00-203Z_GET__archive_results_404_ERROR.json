{"timestamp": "2025-07-24T00:32:00.203Z", "request": {"method": "get", "url": "/archive/results", "headers": {"Accept": "application/json", "Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************.4g_x49gboNWr_5UcPbtOgCik9VJmyjrRXDqveoGmSoY", "User-Agent": "ATMA-E2E-Test-Client/1.0.0", "Accept-Encoding": "gzip, compress, deflate, br"}}, "response": {"status": 404, "statusText": "Not Found", "headers": {"content-security-policy": "default-src 'self'", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "strict-origin-when-cross-origin", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "DENY", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "1; mode=block", "vary": "Origin, Accept-Encoding", "access-control-allow-credentials": "true", "ratelimit-policy": "2000;w=900", "ratelimit-limit": "2000", "ratelimit-remaining": "1999", "ratelimit-reset": "900", "x-powered-by": "Express", "x-request-id": "9mizvcd8orj", "content-type": "application/json; charset=utf-8", "content-length": "80", "etag": "W/\"50-spsdUoPgahL1t3jWDTG6mD1Fp5Y\"", "date": "Thu, 24 Jul 2025 00:32:00 GMT", "connection": "keep-alive", "keep-alive": "timeout=5", "x-gateway": "ATMA-API-Gateway", "x-gateway-version": "1.0.0"}, "data": {"success": false, "error": {"code": "NOT_FOUND", "message": "Route GET / not found"}}}, "isError": true, "headerValidation": {"hasSecurityHeaders": true, "hasRateLimitHeaders": false}}