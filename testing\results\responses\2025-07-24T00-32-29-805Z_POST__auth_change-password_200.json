{"timestamp": "2025-07-24T00:32:29.805Z", "request": {"method": "post", "url": "/auth/change-password", "headers": {"Accept": "application/json", "Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************.bZkR0vsIZ_DSymtM8kzZpdax02QuD77tIQMmXJuL5gY", "User-Agent": "ATMA-E2E-Test-Client/1.0.0", "Content-Length": "72", "Accept-Encoding": "gzip, compress, deflate, br"}, "data": "{\"currentPassword\":\"TestPassword123!2\",\"newPassword\":\"TestPassword123!\"}"}, "response": {"status": 200, "statusText": "OK", "headers": {"content-security-policy": "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "vary": "Origin, Accept-Encoding", "access-control-allow-credentials": "true", "ratelimit-policy": "5000;w=900", "ratelimit-limit": "5000", "ratelimit-remaining": "4961", "ratelimit-reset": "848", "cross-origin-embedder-policy": "require-corp", "x-request-id": "b3f5da00-aee7-476d-b9e9-cca940e3eda7", "content-type": "application/json; charset=utf-8", "content-length": "58", "etag": "W/\"3a-q68W5NWhqX5poAtHZphYySdDRQ8\"", "date": "Thu, 24 Jul 2025 00:32:29 GMT", "connection": "keep-alive", "keep-alive": "timeout=5", "x-gateway": "ATMA-API-Gateway", "x-gateway-version": "1.0.0"}, "data": {"success": true, "message": "Password changed successfully"}}, "isError": false, "headerValidation": {"hasSecurityHeaders": true, "hasRateLimitHeaders": false}}