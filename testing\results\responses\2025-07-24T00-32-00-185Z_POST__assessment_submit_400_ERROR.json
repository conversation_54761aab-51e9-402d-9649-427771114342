{"timestamp": "2025-07-24T00:32:00.185Z", "request": {"method": "post", "url": "/assessment/submit", "headers": {"Accept": "application/json", "Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************.4g_x49gboNWr_5UcPbtOgCik9VJmyjrRXDqveoGmSoY", "User-Agent": "ATMA-E2E-Test-Client/1.0.0", "Content-Length": "631", "Accept-Encoding": "gzip, compress, deflate, br"}, "data": "{\"assessmentName\":\"E2E Test Assessment\",\"riasec\":{\"realistic\":86,\"investigative\":77,\"artistic\":86,\"social\":93,\"enterprising\":86,\"conventional\":58},\"ocean\":{\"openness\":82,\"conscientiousness\":87,\"extraversion\":86,\"agreeableness\":56,\"neuroticism\":83},\"viaIs\":{\"creativity\":90,\"curiosity\":58,\"judgment\":64,\"loveOfLearning\":76,\"perspective\":58,\"bravery\":94,\"perseverance\":60,\"honesty\":58,\"zest\":64,\"love\":66,\"kindness\":75,\"socialIntelligence\":40,\"teamwork\":84,\"fairness\":67,\"leadership\":94,\"forgiveness\":54,\"humility\":77,\"prudence\":75,\"selfRegulation\":44,\"appreciationOfBeauty\":90,\"gratitude\":89,\"hope\":95,\"humor\":73,\"spirituality\":47}}"}, "response": {"status": 400, "statusText": "Bad Request", "headers": {"content-security-policy": "default-src 'self';style-src 'self' 'unsafe-inline';script-src 'self';img-src 'self' data: https:;base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';object-src 'none';script-src-attr 'none';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "vary": "Origin, Accept-Encoding", "access-control-allow-credentials": "true", "ratelimit-policy": "1000;w=3600", "ratelimit-limit": "1000", "ratelimit-remaining": "999", "ratelimit-reset": "3600", "x-powered-by": "Express", "x-idempotency-supported": "true", "x-idempotency-ttl-hours": "24", "content-type": "application/json; charset=utf-8", "content-length": "208", "etag": "W/\"d0-Z/U28PJ/Lv9PXjmeYSP/t00DHDI\"", "date": "Thu, 24 Jul 2025 00:32:00 GMT", "connection": "keep-alive", "keep-alive": "timeout=5", "x-gateway": "ATMA-API-Gateway", "x-gateway-version": "1.0.0"}, "data": {"success": false, "error": {"code": "VALIDATION_ERROR", "message": "Validation failed", "details": {"assessmentName": "Assessment name must be one of: AI-Driven Talent Mapping, AI-Based IQ Test, Custom Assessment"}}}}, "isError": true, "headerValidation": {"hasSecurityHeaders": false, "hasRateLimitHeaders": false}}