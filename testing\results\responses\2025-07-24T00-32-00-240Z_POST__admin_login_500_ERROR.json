{"timestamp": "2025-07-24T00:32:00.240Z", "request": {"method": "post", "url": "/admin/login", "headers": {"Accept": "application/json", "Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************.4g_x49gboNWr_5UcPbtOgCik9VJmyjrRXDqveoGmSoY", "User-Agent": "ATMA-E2E-Test-Client/1.0.0", "Content-Length": "42", "Accept-Encoding": "gzip, compress, deflate, br"}, "data": "{\"username\":\"admin\",\"password\":\"admin123\"}"}, "response": {"status": 500, "statusText": "Internal Server Error", "headers": {"content-security-policy": "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "vary": "Origin, Accept-Encoding", "access-control-allow-credentials": "true", "ratelimit-policy": "2500;w=900", "ratelimit-limit": "2500", "ratelimit-remaining": "2495", "ratelimit-reset": "878", "cross-origin-embedder-policy": "require-corp", "x-request-id": "3ae7c6fa-59ae-4a69-b7af-2395f37f7d37", "content-type": "application/json; charset=utf-8", "content-length": "97", "etag": "W/\"61-bJJnY/X9XBwtUM1YQjiGWOOEb/8\"", "date": "Thu, 24 Jul 2025 00:32:00 GMT", "connection": "keep-alive", "keep-alive": "timeout=5", "x-gateway": "ATMA-API-Gateway", "x-gateway-version": "1.0.0"}, "data": {"success": false, "error": {"code": "INTERNAL_ERROR", "message": "An internal server error occurred"}}}, "isError": true, "headerValidation": {"hasSecurityHeaders": true, "hasRateLimitHeaders": false}}