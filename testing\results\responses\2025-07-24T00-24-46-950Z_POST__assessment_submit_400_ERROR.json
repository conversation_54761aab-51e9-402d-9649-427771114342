{"timestamp": "2025-07-24T00:24:46.950Z", "request": {"method": "post", "url": "/assessment/submit", "headers": {"Accept": "application/json", "Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************.w_HH3B1Njrv1Q1MEpM5UIw-aKVGD0wqX6Dn8_-FoAfQ", "User-Agent": "ATMA-E2E-Test-Client/1.0.0", "Content-Length": "631", "Accept-Encoding": "gzip, compress, deflate, br"}, "data": "{\"assessmentName\":\"E2E Test Assessment\",\"riasec\":{\"realistic\":57,\"investigative\":52,\"artistic\":71,\"social\":63,\"enterprising\":57,\"conventional\":90},\"ocean\":{\"openness\":83,\"conscientiousness\":40,\"extraversion\":42,\"agreeableness\":70,\"neuroticism\":74},\"viaIs\":{\"creativity\":64,\"curiosity\":56,\"judgment\":78,\"loveOfLearning\":68,\"perspective\":82,\"bravery\":62,\"perseverance\":48,\"honesty\":49,\"zest\":58,\"love\":43,\"kindness\":60,\"socialIntelligence\":70,\"teamwork\":76,\"fairness\":92,\"leadership\":80,\"forgiveness\":66,\"humility\":52,\"prudence\":41,\"selfRegulation\":40,\"appreciationOfBeauty\":80,\"gratitude\":80,\"hope\":78,\"humor\":76,\"spirituality\":76}}"}, "response": {"status": 400, "statusText": "Bad Request", "headers": {"content-security-policy": "default-src 'self';style-src 'self' 'unsafe-inline';script-src 'self';img-src 'self' data: https:;base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';object-src 'none';script-src-attr 'none';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "vary": "Origin, Accept-Encoding", "access-control-allow-credentials": "true", "ratelimit-policy": "1000;w=3600", "ratelimit-limit": "1000", "ratelimit-remaining": "999", "ratelimit-reset": "3600", "x-powered-by": "Express", "x-idempotency-supported": "true", "x-idempotency-ttl-hours": "24", "content-type": "application/json; charset=utf-8", "content-length": "208", "etag": "W/\"d0-Z/U28PJ/Lv9PXjmeYSP/t00DHDI\"", "date": "Thu, 24 Jul 2025 00:24:46 GMT", "connection": "keep-alive", "keep-alive": "timeout=5", "x-gateway": "ATMA-API-Gateway", "x-gateway-version": "1.0.0"}, "data": {"success": false, "error": {"code": "VALIDATION_ERROR", "message": "Validation failed", "details": {"assessmentName": "Assessment name must be one of: AI-Driven Talent Mapping, AI-Based IQ Test, Custom Assessment"}}}}, "isError": true, "headerValidation": {"hasSecurityHeaders": false, "hasRateLimitHeaders": false}}