{"timestamp": "2025-07-24T00:22:09.701Z", "request": {"method": "put", "url": "/auth/profile", "headers": {"Accept": "application/json", "Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************.o-l9X3F-vZEgkg0ihAY6_6tMOmOHdIt_iH7DND1wdOE", "User-Agent": "ATMA-E2E-Test-Client/1.0.0", "Content-Length": "212", "Accept-Encoding": "gzip, compress, deflate, br"}, "data": "{\"username\":\"bethany.schuster-swift529606\",\"full_name\":\"<PERSON>\",\"bio\":\"Coepi deprimo tenetur ultio certus in adimpleo chirographum adfero.\",\"location\":\"Fort Kaiabury\",\"website\":\"https://urban-skyline.biz/\"}"}, "response": {"status": 400, "statusText": "Bad Request", "headers": {"content-security-policy": "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "vary": "Origin, Accept-Encoding", "access-control-allow-credentials": "true", "ratelimit-policy": "5000;w=900", "ratelimit-limit": "5000", "ratelimit-remaining": "4961", "ratelimit-reset": "499", "cross-origin-embedder-policy": "require-corp", "x-request-id": "4d399fc4-5f12-4a3b-b074-ad67ad476cf4", "content-type": "application/json; charset=utf-8", "content-length": "148", "etag": "W/\"94-zeUjPB7JU91p2A/pKwKIXUU6cOU\"", "date": "Thu, 24 Jul 2025 00:22:09 GMT", "connection": "keep-alive", "keep-alive": "timeout=5", "x-gateway": "ATMA-API-Gateway", "x-gateway-version": "1.0.0"}, "data": {"success": false, "error": {"code": "VALIDATION_ERROR", "message": "Validation failed", "details": ["Username must contain only alphanumeric characters"]}}}, "isError": true, "headerValidation": {"hasSecurityHeaders": true, "hasRateLimitHeaders": false}}