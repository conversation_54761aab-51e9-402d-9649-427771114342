{"timestamp": "2025-07-24T00:24:47.045Z", "request": {"method": "post", "url": "/auth/schools", "headers": {"Accept": "application/json", "Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************.w_HH3B1Njrv1Q1MEpM5UIw-aKVGD0wqX6Dn8_-FoAfQ", "User-Agent": "ATMA-E2E-Test-Client/1.0.0", "Content-Length": "109", "Accept-Encoding": "gzip, compress, deflate, br"}, "data": "{\"name\":\"Heathcote - Konopelski School\",\"address\":\"393 Rosina Row\",\"city\":\"Cristcester\",\"province\":\"Georgia\"}"}, "response": {"status": 201, "statusText": "Created", "headers": {"content-security-policy": "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "vary": "Origin, Accept-Encoding", "access-control-allow-credentials": "true", "ratelimit-policy": "5000;w=900", "ratelimit-limit": "5000", "ratelimit-remaining": "4939", "ratelimit-reset": "341", "cross-origin-embedder-policy": "require-corp", "x-request-id": "2f343849-18fd-4d28-894f-e84cb44b062d", "content-type": "application/json; charset=utf-8", "content-length": "252", "etag": "W/\"fc-ACtHKziNj95E3cc/A8kXqto/n1I\"", "date": "Thu, 24 Jul 2025 00:24:47 GMT", "connection": "keep-alive", "keep-alive": "timeout=5", "x-gateway": "ATMA-API-Gateway", "x-gateway-version": "1.0.0"}, "data": {"success": true, "message": "Success", "data": {"school": {"created_at": "2025-07-24T00:24:47.036Z", "id": 16, "name": "Heathcote - Konopelski School", "address": "393 <PERSON><PERSON><PERSON>", "city": "Cristcester", "province": "Georgia"}, "message": "School created successfully"}}}, "isError": false, "headerValidation": {"hasSecurityHeaders": true, "hasRateLimitHeaders": false}}