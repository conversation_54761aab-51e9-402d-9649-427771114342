{"timestamp": "2025-07-24T00:24:47.438Z", "request": {"method": "get", "url": "/auth/profile", "headers": {"Accept": "application/json", "Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************.NfzqZwR95w2FnMSOMTUEMkrk-mFTOMWp39yQcij0GK8", "User-Agent": "ATMA-E2E-Test-Client/1.0.0", "Accept-Encoding": "gzip, compress, deflate, br"}}, "response": {"status": 200, "statusText": "OK", "headers": {"content-security-policy": "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "vary": "Origin, Accept-Encoding", "access-control-allow-credentials": "true", "ratelimit-policy": "5000;w=900", "ratelimit-limit": "5000", "ratelimit-remaining": "4924", "ratelimit-reset": "341", "cross-origin-embedder-policy": "require-corp", "x-request-id": "dfee52ac-d1fa-4657-a896-d9d69e7c1f08", "content-type": "application/json; charset=utf-8", "content-length": "272", "etag": "W/\"110-ngNl+Q72NJrRYrazfCtabFVdK4c\"", "date": "Thu, 24 Jul 2025 00:24:47 GMT", "connection": "keep-alive", "keep-alive": "timeout=5", "x-gateway": "ATMA-API-Gateway", "x-gateway-version": "1.0.0"}, "data": {"success": true, "message": "Success", "data": {"user": {"id": "3f9b3b34-1147-47d7-9d23-a4e0a01ec57d", "username": null, "email": "<EMAIL>", "user_type": "user", "is_active": true, "token_balance": 3, "last_login": null, "created_at": "2025-07-24T00:24:47.285Z", "profile": null}}}}, "isError": false, "headerValidation": {"hasSecurityHeaders": true, "hasRateLimitHeaders": false}}