{"timestamp": "2025-07-24T00:32:29.874Z", "request": {"method": "post", "url": "/assessment/submit", "headers": {"Accept": "application/json", "Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************.bZkR0vsIZ_DSymtM8kzZpdax02QuD77tIQMmXJuL5gY", "User-Agent": "ATMA-E2E-Test-Client/1.0.0", "Content-Length": "631", "Accept-Encoding": "gzip, compress, deflate, br"}, "data": "{\"assessmentName\":\"E2E Test Assessment\",\"riasec\":{\"realistic\":86,\"investigative\":63,\"artistic\":47,\"social\":52,\"enterprising\":49,\"conventional\":89},\"ocean\":{\"openness\":45,\"conscientiousness\":61,\"extraversion\":44,\"agreeableness\":71,\"neuroticism\":64},\"viaIs\":{\"creativity\":91,\"curiosity\":51,\"judgment\":43,\"loveOfLearning\":60,\"perspective\":83,\"bravery\":86,\"perseverance\":54,\"honesty\":76,\"zest\":46,\"love\":74,\"kindness\":80,\"socialIntelligence\":57,\"teamwork\":94,\"fairness\":88,\"leadership\":95,\"forgiveness\":51,\"humility\":44,\"prudence\":40,\"selfRegulation\":82,\"appreciationOfBeauty\":73,\"gratitude\":61,\"hope\":73,\"humor\":86,\"spirituality\":81}}"}, "response": {"status": 400, "statusText": "Bad Request", "headers": {"content-security-policy": "default-src 'self';style-src 'self' 'unsafe-inline';script-src 'self';img-src 'self' data: https:;base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';object-src 'none';script-src-attr 'none';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "vary": "Origin, Accept-Encoding", "access-control-allow-credentials": "true", "ratelimit-policy": "1000;w=3600", "ratelimit-limit": "1000", "ratelimit-remaining": "999", "ratelimit-reset": "3600", "x-powered-by": "Express", "x-idempotency-supported": "true", "x-idempotency-ttl-hours": "24", "content-type": "application/json; charset=utf-8", "content-length": "208", "etag": "W/\"d0-Z/U28PJ/Lv9PXjmeYSP/t00DHDI\"", "date": "Thu, 24 Jul 2025 00:32:29 GMT", "connection": "keep-alive", "keep-alive": "timeout=5", "x-gateway": "ATMA-API-Gateway", "x-gateway-version": "1.0.0"}, "data": {"success": false, "error": {"code": "VALIDATION_ERROR", "message": "Validation failed", "details": {"assessmentName": "Assessment name must be one of: AI-Driven Talent Mapping, AI-Based IQ Test, Custom Assessment"}}}}, "isError": true, "headerValidation": {"hasSecurityHeaders": false, "hasRateLimitHeaders": false}}