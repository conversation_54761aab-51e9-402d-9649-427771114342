{"timestamp": "2025-07-24T00:21:59.237Z", "request": {"method": "post", "url": "/auth/register", "headers": {"Accept": "application/json", "Content-Type": "application/json", "User-Agent": "ATMA-E2E-Test-Client/1.0.0", "Content-Length": "96", "Accept-Encoding": "gzip, compress, deflate, br"}, "data": "{\"email\":\"<EMAIL>\",\"password\":\"TestPassword123!\",\"username\":\"delpha.watsica519117\"}"}, "response": {"status": 201, "statusText": "Created", "headers": {"content-security-policy": "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "vary": "Origin, Accept-Encoding", "access-control-allow-credentials": "true", "ratelimit-policy": "2500;w=900", "ratelimit-limit": "2500", "ratelimit-remaining": "2482", "ratelimit-reset": "509", "cross-origin-embedder-policy": "require-corp", "x-request-id": "f56f5b98-603c-4607-ab2f-502143f0ca47", "content-type": "application/json; charset=utf-8", "content-length": "662", "etag": "W/\"296-iLRlSDgzygTJkYFDGIn8CNhyWaw\"", "date": "Thu, 24 Jul 2025 00:21:59 GMT", "connection": "keep-alive", "keep-alive": "timeout=5", "x-gateway": "ATMA-API-Gateway", "x-gateway-version": "1.0.0"}, "data": {"success": true, "message": "User registered successfully", "data": {"user": {"created_at": "2025-07-24T00:21:59.227Z", "updated_at": "2025-07-24T00:21:59.227Z", "id": "bd4ce88c-0a02-48ef-aeeb-d5b0a2c2836c", "is_active": true, "email": "<EMAIL>", "user_type": "user", "token_balance": 3, "username": null, "last_login": null}, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************.6fArYXKDBXn-a9AkaAg437vPfxd0zyeYRnCPt0NKsTo"}}}, "isError": false, "headerValidation": {"hasSecurityHeaders": true, "hasRateLimitHeaders": false}}