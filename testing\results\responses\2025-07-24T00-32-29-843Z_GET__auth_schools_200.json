{"timestamp": "2025-07-24T00:32:29.843Z", "request": {"method": "get", "url": "/auth/schools", "headers": {"Accept": "application/json", "Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************.bZkR0vsIZ_DSymtM8kzZpdax02QuD77tIQMmXJuL5gY", "User-Agent": "ATMA-E2E-Test-Client/1.0.0", "Accept-Encoding": "gzip, compress, deflate, br"}}, "response": {"status": 200, "statusText": "OK", "headers": {"content-security-policy": "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "vary": "Origin, Accept-Encoding", "access-control-allow-credentials": "true", "ratelimit-policy": "5000;w=900", "ratelimit-limit": "5000", "ratelimit-remaining": "4959", "ratelimit-reset": "848", "cross-origin-embedder-policy": "require-corp", "x-request-id": "2cbafaa9-180d-46c2-b836-4294410138ac", "content-type": "application/json; charset=utf-8", "content-length": "908", "etag": "W/\"38c-I+d4NsQWxbNP4/F8nqdd2iAUw2c\"", "date": "Thu, 24 Jul 2025 00:32:29 GMT", "connection": "keep-alive", "keep-alive": "timeout=5", "x-gateway": "ATMA-API-Gateway", "x-gateway-version": "1.0.0"}, "data": {"success": true, "message": "Success", "data": {"schools": [{"id": 17, "name": "Dietrich, <PERSON><PERSON> and Nikolaus School", "address": "84544 McKenzie Route", "city": "Fort Devantown", "province": "Maine", "created_at": "2025-07-24T00:32:00.255Z"}, {"id": 16, "name": "Heathcote - Konopelski School", "address": "393 <PERSON><PERSON><PERSON>", "city": "Cristcester", "province": "Georgia", "created_at": "2025-07-24T00:24:47.036Z"}, {"id": 15, "name": "Mosciski Group School", "address": "92438 Stracke Brook", "city": "Fort Isadore", "province": "California", "created_at": "2025-07-24T00:22:09.827Z"}, {"id": 1, "name": "SMA Negeri 1 Jakarta", "address": "<PERSON><PERSON>. <PERSON><PERSON> Utomo No. 7", "city": "Jakarta Pusat", "province": "DKI Jakarta", "created_at": "2025-07-23T23:11:34.758Z"}, {"id": 5, "name": "SMA Negeri 1 Medan", "address": "Jl. Mistar No. 7", "city": "Medan", "province": "<PERSON><PERSON><PERSON>", "created_at": "2025-07-23T23:11:34.758Z"}], "pagination": {"total": 17, "page": 1, "limit": 5, "pages": 4}}}}, "isError": false, "headerValidation": {"hasSecurityHeaders": true, "hasRateLimitHeaders": false}}