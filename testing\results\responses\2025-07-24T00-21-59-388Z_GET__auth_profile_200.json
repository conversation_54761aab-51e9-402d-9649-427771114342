{"timestamp": "2025-07-24T00:21:59.388Z", "request": {"method": "get", "url": "/auth/profile", "headers": {"Accept": "application/json", "Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************.6fArYXKDBXn-a9AkaAg437vPfxd0zyeYRnCPt0NKsTo", "User-Agent": "ATMA-E2E-Test-Client/1.0.0", "Accept-Encoding": "gzip, compress, deflate, br"}}, "response": {"status": 200, "statusText": "OK", "headers": {"content-security-policy": "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "vary": "Origin, Accept-Encoding", "access-control-allow-credentials": "true", "ratelimit-policy": "5000;w=900", "ratelimit-limit": "5000", "ratelimit-remaining": "4972", "ratelimit-reset": "509", "cross-origin-embedder-policy": "require-corp", "x-request-id": "6042dc25-b443-43d1-935c-92626ec185c6", "content-type": "application/json; charset=utf-8", "content-length": "272", "etag": "W/\"110-CJJyTgzTHDwk7xfktp1+Yqno03M\"", "date": "Thu, 24 Jul 2025 00:21:59 GMT", "connection": "keep-alive", "keep-alive": "timeout=5", "x-gateway": "ATMA-API-Gateway", "x-gateway-version": "1.0.0"}, "data": {"success": true, "message": "Success", "data": {"user": {"id": "bd4ce88c-0a02-48ef-aeeb-d5b0a2c2836c", "username": null, "email": "<EMAIL>", "user_type": "user", "is_active": true, "token_balance": 3, "last_login": null, "created_at": "2025-07-24T00:21:59.227Z", "profile": null}}}}, "isError": false, "headerValidation": {"hasSecurityHeaders": true, "hasRateLimitHeaders": false}}