{"timestamp": "2025-07-24T00:31:47.763Z", "request": {"method": "post", "url": "/auth/login", "headers": {"Accept": "application/json", "Content-Type": "application/json", "User-Agent": "ATMA-E2E-Test-Client/1.0.0", "Content-Length": "62", "Accept-Encoding": "gzip, compress, deflate, br"}, "data": "{\"email\":\"<EMAIL>\",\"password\":\"TestPassword123!\"}"}, "response": {"status": 200, "statusText": "OK", "headers": {"content-security-policy": "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "vary": "Origin, Accept-Encoding", "access-control-allow-credentials": "true", "ratelimit-policy": "2500;w=900", "ratelimit-limit": "2500", "ratelimit-remaining": "2497", "ratelimit-reset": "890", "cross-origin-embedder-policy": "require-corp", "x-request-id": "b70f12e1-d751-49b5-a254-380c4a37d994", "content-type": "application/json; charset=utf-8", "content-length": "672", "etag": "W/\"2a0-lCEkJzPwDtli6lNVG8tYDaQZbSw\"", "date": "Thu, 24 Jul 2025 00:31:47 GMT", "connection": "keep-alive", "keep-alive": "timeout=5", "x-gateway": "ATMA-API-Gateway", "x-gateway-version": "1.0.0"}, "data": {"success": true, "message": "Login successful", "data": {"user": {"id": "135cc6e0-d26d-453d-a617-1c470a82b115", "username": null, "email": "<EMAIL>", "user_type": "user", "is_active": true, "token_balance": 3, "last_login": "2025-07-24T00:31:47.762Z", "created_at": "2025-07-24T00:31:47.665Z", "updated_at": "2025-07-24T00:31:47.762Z"}, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************.p8HX1l2l3EzdVGRPcDs0GEWMwpGpdbeM8WsEQYMPhhE"}}}, "isError": false, "headerValidation": {"hasSecurityHeaders": true, "hasRateLimitHeaders": false}}