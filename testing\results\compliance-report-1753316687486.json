{"timestamp": "2025-07-24T00:24:47.486Z", "compliance": {"percentage": "15.38", "status": "NON_COMPLIANT"}, "summary": {"passed": 2, "failed": 19, "total": 13, "compliance": "15.38"}, "details": {"api_tests": {"passed": 2, "failed": 13, "total": 9, "errors": ["PUT /auth/profile: Request failed with status code 400", "Auth endpoints: Request failed with status code 400", "POST /assessment/submit: Request failed with status code 400", "Assessment endpoints: Request failed with status code 400", "GET /archive/results: Request failed with status code 404", "Archive endpoints: Request failed with status code 404", "GET /chatbot/conversations: Request failed with status code 500", "Chatbot endpoints: Request failed with status code 500", "POST /admin/login: Request failed with status code 500", "GET /health: Request failed with status code 404", "Health endpoints: Request failed with status code 404", "GET /auth/profile (unauthorized): Error response missing or invalid success field", "Error scenarios: Error response missing or invalid success field"]}, "websocket_tests": {"passed": 0, "failed": 2, "total": 0, "errors": ["Basic connection: websocket error", "websocket error"]}, "header_tests": {"passed": 0, "failed": 4, "total": 4, "errors": ["Security headers test failed: Request failed with status code 404", "Rate limiting test failed: Cannot read properties of undefined (reading 'x-ratelimit-limit')", "Response format test failed: Request failed with status code 404", "Error response format test failed: Error response format validation failed: Error response missing \"error.code\" field, Error response missing \"error.message\" field, Error response missing or invalid \"error.timestamp\" field"]}}, "specifications": {"api_gateway_external": "api-gateway/api_external.md", "websocket_manual": "notification-service/WEBSOCKET_MANUAL.md"}, "recommendations": ["Review failed test cases and update implementation to match specifications", "API endpoints need to be updated to match api_external.md specification", "WebSocket implementation needs to be updated to match WEBSOCKET_MANUAL.md specification", "HTTP headers need to be updated to match API Gateway security requirements"]}