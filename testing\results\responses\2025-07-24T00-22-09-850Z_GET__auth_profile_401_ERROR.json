{"timestamp": "2025-07-24T00:22:09.850Z", "request": {"method": "get", "url": "/auth/profile", "headers": {"Accept": "application/json", "Content-Type": "application/json", "User-Agent": "ATMA-E2E-Test-Client/1.0.0", "Accept-Encoding": "gzip, compress, deflate, br"}}, "response": {"status": 401, "statusText": "Unauthorized", "headers": {"content-security-policy": "default-src 'self';style-src 'self' 'unsafe-inline';script-src 'self';img-src 'self' data: https:;base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';object-src 'none';script-src-attr 'none';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "vary": "Origin, Accept-Encoding", "access-control-allow-credentials": "true", "ratelimit-policy": "5000;w=900", "ratelimit-limit": "5000", "ratelimit-remaining": "4954", "ratelimit-reset": "498", "content-type": "application/json; charset=utf-8", "content-length": "77", "etag": "W/\"4d-J/wWO9LreAy34v3LBqwMIZZ4YQw\"", "date": "Thu, 24 Jul 2025 00:22:09 GMT", "connection": "keep-alive", "keep-alive": "timeout=5"}, "data": {"success": false, "error": "UNAUTHORIZED", "message": "Access token is required"}}, "isError": true, "headerValidation": {"hasSecurityHeaders": false, "hasRateLimitHeaders": false}}