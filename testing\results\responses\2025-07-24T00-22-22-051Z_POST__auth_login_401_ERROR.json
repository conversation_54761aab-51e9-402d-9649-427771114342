{"timestamp": "2025-07-24T00:22:22.051Z", "request": {"method": "post", "url": "/auth/login", "headers": {"Accept": "application/json", "Content-Type": "application/json", "User-Agent": "ATMA-E2E-Test-Client/1.0.0", "Content-Length": "55", "Accept-Encoding": "gzip, compress, deflate, br"}, "data": "{\"email\":\"<EMAIL>\",\"password\":\"wrongpassword\"}"}, "response": {"status": 401, "statusText": "Unauthorized", "headers": {"content-security-policy": "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "vary": "Origin, Accept-Encoding", "access-control-allow-credentials": "true", "ratelimit-policy": "2500;w=900", "ratelimit-limit": "2500", "ratelimit-remaining": "2477", "ratelimit-reset": "486", "cross-origin-embedder-policy": "require-corp", "x-request-id": "dcc35d88-6914-4603-819e-02bc624bf8bf", "content-type": "application/json; charset=utf-8", "content-length": "94", "etag": "W/\"5e-9T9AtAxLyKTGnuY/QFcHTB/LfOo\"", "date": "Thu, 24 Jul 2025 00:22:22 GMT", "connection": "keep-alive", "keep-alive": "timeout=5", "x-gateway": "ATMA-API-Gateway", "x-gateway-version": "1.0.0"}, "data": {"success": false, "error": {"code": "INVALID_CREDENTIALS", "message": "Invalid email or password"}}}, "isError": true, "headerValidation": {"hasSecurityHeaders": true, "hasRateLimitHeaders": false}}