{"timestamp": "2025-07-24T00:22:09.834Z", "request": {"method": "post", "url": "/auth/schools", "headers": {"Accept": "application/json", "Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************.o-l9X3F-vZEgkg0ihAY6_6tMOmOHdIt_iH7DND1wdOE", "User-Agent": "ATMA-E2E-Test-Client/1.0.0", "Content-Length": "110", "Accept-Encoding": "gzip, compress, deflate, br"}, "data": "{\"name\":\"Mosciski Group School\",\"address\":\"92438 Stracke Brook\",\"city\":\"Fort Isadore\",\"province\":\"California\"}"}, "response": {"status": 201, "statusText": "Created", "headers": {"content-security-policy": "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "vary": "Origin, Accept-Encoding", "access-control-allow-credentials": "true", "ratelimit-policy": "5000;w=900", "ratelimit-limit": "5000", "ratelimit-remaining": "4956", "ratelimit-reset": "498", "cross-origin-embedder-policy": "require-corp", "x-request-id": "332d153b-22d5-49e3-92da-e7b95dbd7ac4", "content-type": "application/json; charset=utf-8", "content-length": "253", "etag": "W/\"fd-jtgKmDqzOUPZD7qCqhZHCPpYjOU\"", "date": "Thu, 24 Jul 2025 00:22:09 GMT", "connection": "keep-alive", "keep-alive": "timeout=5", "x-gateway": "ATMA-API-Gateway", "x-gateway-version": "1.0.0"}, "data": {"success": true, "message": "Success", "data": {"school": {"created_at": "2025-07-24T00:22:09.827Z", "id": 15, "name": "Mosciski Group School", "address": "92438 Stracke Brook", "city": "Fort Isadore", "province": "California"}, "message": "School created successfully"}}}, "isError": false, "headerValidation": {"hasSecurityHeaders": true, "hasRateLimitHeaders": false}}