{"timestamp": "2025-07-24T00:32:00.262Z", "request": {"method": "post", "url": "/auth/schools", "headers": {"Accept": "application/json", "Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************.4g_x49gboNWr_5UcPbtOgCik9VJmyjrRXDqveoGmSoY", "User-Agent": "ATMA-E2E-Test-Client/1.0.0", "Content-Length": "121", "Accept-Encoding": "gzip, compress, deflate, br"}, "data": "{\"name\":\"Dietrich, <PERSON> and Nikolaus School\",\"address\":\"84544 McKenzie Route\",\"city\":\"Fort Devantown\",\"province\":\"Maine\"}"}, "response": {"status": 201, "statusText": "Created", "headers": {"content-security-policy": "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "vary": "Origin, Accept-Encoding", "access-control-allow-credentials": "true", "ratelimit-policy": "5000;w=900", "ratelimit-limit": "5000", "ratelimit-remaining": "4969", "ratelimit-reset": "878", "cross-origin-embedder-policy": "require-corp", "x-request-id": "eb98aa75-2a0f-4fd5-952e-57929284f773", "content-type": "application/json; charset=utf-8", "content-length": "264", "etag": "W/\"108-8ySJ6137is7v7B4JhzVh5rDAT7k\"", "date": "Thu, 24 Jul 2025 00:32:00 GMT", "connection": "keep-alive", "keep-alive": "timeout=5", "x-gateway": "ATMA-API-Gateway", "x-gateway-version": "1.0.0"}, "data": {"success": true, "message": "Success", "data": {"school": {"created_at": "2025-07-24T00:32:00.255Z", "id": 17, "name": "Dietrich, <PERSON><PERSON> and Nikolaus School", "address": "84544 McKenzie Route", "city": "Fort Devantown", "province": "Maine"}, "message": "School created successfully"}}}, "isError": false, "headerValidation": {"hasSecurityHeaders": true, "hasRateLimitHeaders": false}}