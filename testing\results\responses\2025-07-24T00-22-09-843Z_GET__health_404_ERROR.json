{"timestamp": "2025-07-24T00:22:09.843Z", "request": {"method": "get", "url": "/health", "headers": {"Accept": "application/json", "Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************.o-l9X3F-vZEgkg0ihAY6_6tMOmOHdIt_iH7DND1wdOE", "User-Agent": "ATMA-E2E-Test-Client/1.0.0", "Accept-Encoding": "gzip, compress, deflate, br"}}, "response": {"status": 404, "statusText": "Not Found", "headers": {"content-security-policy": "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "vary": "Origin, Accept-Encoding", "access-control-allow-credentials": "true", "ratelimit-policy": "5000;w=900", "ratelimit-limit": "5000", "ratelimit-remaining": "4955", "ratelimit-reset": "498", "cross-origin-embedder-policy": "require-corp", "x-request-id": "0c275e3e-cfee-4a91-862f-0138f1f11190", "content-type": "application/json; charset=utf-8", "content-length": "90", "etag": "W/\"5a-R/uQGewLwYMky9fAzrxb18NcVkg\"", "date": "Thu, 24 Jul 2025 00:22:09 GMT", "connection": "keep-alive", "keep-alive": "timeout=5", "x-gateway": "ATMA-API-Gateway", "x-gateway-version": "1.0.0"}, "data": {"success": false, "error": {"code": "NOT_FOUND", "message": "Route GET /api/health not found"}}}, "isError": true, "headerValidation": {"hasSecurityHeaders": true, "hasRateLimitHeaders": false}}