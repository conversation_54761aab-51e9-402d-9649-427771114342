{"timestamp": "2025-07-24T00:32:29.963Z", "request": {"method": "post", "url": "/auth/schools", "headers": {"Accept": "application/json", "Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************.bZkR0vsIZ_DSymtM8kzZpdax02QuD77tIQMmXJuL5gY", "User-Agent": "ATMA-E2E-Test-Client/1.0.0", "Content-Length": "117", "Accept-Encoding": "gzip, compress, deflate, br"}, "data": "{\"name\":\"<PERSON><PERSON><PERSON><PERSON>, Ko<PERSON>p and Beier School\",\"address\":\"84303 Jakayla Islands\",\"city\":\"Tacoma\",\"province\":\"Oklahoma\"}"}, "response": {"status": 201, "statusText": "Created", "headers": {"content-security-policy": "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "vary": "Origin, Accept-Encoding", "access-control-allow-credentials": "true", "ratelimit-policy": "5000;w=900", "ratelimit-limit": "5000", "ratelimit-remaining": "4954", "ratelimit-reset": "848", "cross-origin-embedder-policy": "require-corp", "x-request-id": "185f5e13-bac7-460a-be0c-976aa1386dd9", "content-type": "application/json; charset=utf-8", "content-length": "260", "etag": "W/\"104-w6AazmLoWPg3EzEo6i65gKSPY/k\"", "date": "Thu, 24 Jul 2025 00:32:29 GMT", "connection": "keep-alive", "keep-alive": "timeout=5", "x-gateway": "ATMA-API-Gateway", "x-gateway-version": "1.0.0"}, "data": {"success": true, "message": "Success", "data": {"school": {"created_at": "2025-07-24T00:32:29.957Z", "id": 18, "name": "<PERSON><PERSON><PERSON><PERSON>, Ko<PERSON>p and Beier School", "address": "84303 Jakayla Islands", "city": "Tacoma", "province": "Oklahoma"}, "message": "School created successfully"}}}, "isError": false, "headerValidation": {"hasSecurityHeaders": true, "hasRateLimitHeaders": false}}